<div class="confirm-payment">
    <h2 class="confirm-section-title k11-mt-40">{{ "account.paymentOptions"|trans|sw_sanitize }}</h2>

    {# Express Payment Buttons - oberhalb der normalen Zahlungsarten #}
    <div class="express-payment-buttons mb-3">
        {# Google Pay Express Button #}
        {% set googlePayExtensionName = 'unzerGooglePay' %}
        {{ dump(page.extensions[googlePayExtensionName]) }}
        {% if page.extensions[googlePayExtensionName] %}
            {% set googlePayOptions = page.extensions[googlePayExtensionName].publicConfig|merge({
                currency: context.currency.isoCode,
                amount: summary.price.rawTotal ?: summary.price.totalPrice
            }) %}
            <div class="google-pay-express mb-2">
                <div data-unzer-payment-google-pay data-unzer-payment-google-pay-options="{{ googlePayOptions|json_encode }}">
                    <div id="unzer-google-pay-button"></div>
                </div>
            </div>
        {% endif %}

        {# Apple Pay Express Button #}
        {% set applePayV2ExtensionName = constant("UnzerPayment6\\Components\\Struct\\PageExtension\\Checkout\\Confirm\\ApplePayV2PageExtension::EXTENSION_NAME") %}
        {% if page.extensions[applePayV2ExtensionName] %}
            {% set paymentFrameExtensionName = constant("UnzerPayment6\\Components\\Struct\\PageExtension\\Checkout\\Confirm\\PaymentFramePageExtension::EXTENSION_NAME") %}
            <div class="apple-pay-express mb-2">
                <div data-unzer-payment-apple-pay-v2 data-unzer-payment-apple-pay-v2-options="{{ {
                    countryCode: billingAddress.country.iso,
                    currency: context.currency.isoCode,
                    shopName: page.extensions[paymentFrameExtensionName].shopName,
                    amount: summary.price.rawTotal ?: summary.price.totalPrice,
                    noApplePayMessage: "UnzerPayment.frame.applePay.missingMessage" | trans,
                    supportedNetworks: page.extensions[applePayV2ExtensionName].supportedNetworks,
                    merchantCapabilities: page.extensions[applePayV2ExtensionName].merchantCapabilities,
                }|json_encode }}">
                    <apple-pay-button buttonstyle="black" type="buy" locale="{{ context.salesChannel.locale.code }}" style="--apple-pay-button-width: 100%;"></apple-pay-button>
                </div>
            </div>
        {% endif %}

        {# Trennlinie wenn Express-Buttons vorhanden #}
        {% if page.extensions[googlePayExtensionName] or page.extensions[applePayV2ExtensionName] %}
            <div class="express-payment-divider text-center my-3">
                <span class="text-muted">{{ "checkout.orChoosePaymentMethod"|trans|default("oder wählen Sie eine Zahlungsart") }}</span>
                <hr>
            </div>
        {% endif %}
    </div>

    <div class="card-body">
        <form id="confirmPaymentForm"
              name="confirmPaymentForm"
              action="{{ path('frontend.checkout.configure') }}"
              data-form-csrf-handler="true"
              data-form-auto-submit="true"
              method="post">
                {{ sw_csrf('frontend.checkout.configure') }}
                <input type="hidden" name="redirectTo" value="frontend.checkout.confirm.page">
                {% sw_include '@Storefront/storefront/component/payment/payment-fields.html.twig' with {
                    'defaultPaymentMethodId': context.paymentMethod.id,
                    'visiblePaymentMethodsLimit': 5,
                    'selectedPaymentMethodId': context.paymentMethod.id
                } %}
        </form>
    </div>
</div>
